import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Trash2, Calendar, Clock } from "lucide-react";

interface Event {
  id: string;
  name: string;
  date: Date;
}

interface EventListProps {
  events: Event[];
  onEventDelete: (id: string) => void;
  onEventSelect: (event: Event) => void;
  selectedEventId?: string;
}

export function EventList({ events, onEventDelete, onEventSelect, selectedEventId }: EventListProps) {
  if (events.length === 0) {
    return (
      <div className="text-center py-8 glass-card rounded-xl">
        <div className="text-4xl mb-4">⏰</div>
        <h3 className="text-lg font-semibold mb-2">Δεν υπάρχουν events</h3>
        <p className="text-muted-foreground">Δημιούργησε το πρώτο σου event αντίστροφης μέτρησης για να ξεκινήσεις!</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold mb-4 gradient-text">Τα Events σου</h3>
      {events.map((event) => {
        const isSelected = event.id === selectedEventId;
        const isPast = event.date < new Date();
        
        return (
          <div
            key={event.id}
            className={cn(
              "glass-card p-4 rounded-xl cursor-pointer transition-spring hover:scale-[1.02] group",
              isSelected && "glow-border",
              isPast && "opacity-60"
            )}
            onClick={() => onEventSelect(event)}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold truncate group-hover:gradient-text transition-smooth">
                  {event.name}
                </h4>
                <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {format(event.date, "MMM dd, yyyy")}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {format(event.date, "HH:mm")}
                  </div>
                </div>
                {isPast && (
                  <div className="text-xs text-green-400 mt-1">✅ Ολοκληρώθηκε</div>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onEventDelete(event.id);
                }}
                className="opacity-60 hover:opacity-100 hover:bg-destructive/20 hover:border-destructive/50 transition-smooth"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        );
      })}
    </div>
  );
}

function cn(...classes: (string | undefined | false)[]) {
  return classes.filter(Boolean).join(' ');
}