import { useState, useEffect } from "react";
import { CountdownClock } from "@/components/CountdownClock";
import { EventForm } from "@/components/EventForm";
import { EventList } from "@/components/EventList";

interface Event {
  id: string;
  name: string;
  date: Date;
}

const Index = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);

  // Load events from localStorage on component mount
  useEffect(() => {
    const savedEvents = localStorage.getItem('countdown-events');
    if (savedEvents) {
      try {
        const parsedEvents = JSON.parse(savedEvents);
        const eventsWithDates = parsedEvents.map((event: any) => ({
          ...event,
          date: new Date(event.date)
        }));
        setEvents(eventsWithDates);
        
        // Auto-select the next upcoming event
        const upcomingEvents = eventsWithDates
          .filter((event: Event) => event.date > new Date())
          .sort((a: Event, b: Event) => a.date.getTime() - b.date.getTime());
        
        if (upcomingEvents.length > 0) {
          setSelectedEvent(upcomingEvents[0]);
        }
      } catch (error) {
        console.error('Error loading events:', error);
      }
    }
  }, []);

  // Save events to localStorage whenever events change
  useEffect(() => {
    localStorage.setItem('countdown-events', JSON.stringify(events));
  }, [events]);

  const handleEventCreate = (newEvent: Event) => {
    const updatedEvents = [...events, newEvent];
    setEvents(updatedEvents);
    setSelectedEvent(newEvent);
  };

  const handleEventDelete = (eventId: string) => {
    const updatedEvents = events.filter(event => event.id !== eventId);
    setEvents(updatedEvents);
    
    // If deleted event was selected, select next upcoming event or clear selection
    if (selectedEvent?.id === eventId) {
      const upcomingEvents = updatedEvents
        .filter(event => event.date > new Date())
        .sort((a, b) => a.date.getTime() - b.date.getTime());
      
      setSelectedEvent(upcomingEvents.length > 0 ? upcomingEvents[0] : null);
    }
  };

  const handleEventSelect = (event: Event) => {
    setSelectedEvent(event);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted">
      {/* Background Glow Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/20 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-accent/20 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12 animate-fade-in-up">
          <h1 className="text-5xl font-bold gradient-text mb-4">
            Αντίστροφη Μέτρηση
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Αυτό το Site φτιάχτηκε για να μετράει τις ημέρες υπομονής της Δήμητρας στην δουλειά μέχει να παραιτηθεί.. αλλά και όχι μόνο..
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {/* Left Panel - Event Management */}
          <div className="lg:col-span-1 space-y-6">
            <div className="animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <EventForm onEventCreate={handleEventCreate} />
            </div>
            
            <div className="animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              <EventList
                events={events}
                onEventDelete={handleEventDelete}
                onEventSelect={handleEventSelect}
                selectedEventId={selectedEvent?.id}
              />
            </div>
          </div>

          {/* Right Panel - Countdown Display */}
          <div className="lg:col-span-2 flex items-center justify-center">
            {selectedEvent ? (
              <div className="animate-scale-in">
                <CountdownClock
                  targetDate={selectedEvent.date}
                  eventName={selectedEvent.name}
                />
              </div>
            ) : (
              <div className="text-center glass-card p-12 rounded-2xl animate-fade-in-up">
                <div className="text-6xl mb-6">🎯</div>
                <h2 className="text-2xl font-bold gradient-text mb-4">
                  Έτοιμοι για αντίστροφη μέτρηση;
                </h2>
                <p className="text-muted-foreground text-lg mb-6 max-w-md">
                  Επέλεξε ένα event από τη λίστα σου ή δημιουργήστε ένα νέο για να δεις το χρονόμετρο αντίστροφης μέτρησης σε δράση.
                </p>
                <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full animate-ping" />
                  <span>Περιμένω το πρώτο event...</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-16 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
          <p className="text-muted-foreground">
            Εύχομαι να φανεί χρήσιμο
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;