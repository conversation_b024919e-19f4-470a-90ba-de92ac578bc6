import { useEffect, useState } from "react";

interface CountdownClockProps {
  targetDate: Date;
  eventName: string;
}

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  total: number;
}

export function CountdownClock({ targetDate, eventName }: CountdownClockProps) {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    total: 0,
  });

  const calculateTimeRemaining = (endDate: Date): TimeRemaining => {
    const now = new Date().getTime();
    const target = endDate.getTime();
    const difference = target - now;

    if (difference > 0) {
      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((difference % (1000 * 60)) / 1000),
        total: difference,
      };
    }

    return { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 };
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(calculateTimeRemaining(targetDate));
    }, 1000);

    // Calculate initial time remaining
    setTimeRemaining(calculateTimeRemaining(targetDate));

    return () => clearInterval(timer);
  }, [targetDate]);

  // Calculate rotation for second hand (360 degrees in 60 seconds)
  const secondHandRotation = (60 - timeRemaining.seconds) * 6;

  return (
    <div className="flex flex-col items-center space-y-8 animate-fade-in-up">
      {/* Event Name */}
      <div className="text-center">
        <h2 className="text-3xl font-bold gradient-text mb-2">{eventName}</h2>
        <p className="text-muted-foreground">Countdown Timer</p>
      </div>

      {/* Main Clock Display */}
      <div className="relative">
        {/* Clock Face */}
        <div className="w-80 h-80 rounded-full glass-card glow-border flex items-center justify-center relative overflow-hidden">
          {/* Clock Numbers */}
          {[...Array(12)].map((_, i) => {
            const angle = (i + 1) * 30 - 90; // Start from 12 o'clock
            const radian = (angle * Math.PI) / 180;
            const x = Math.cos(radian) * 120;
            const y = Math.sin(radian) * 120;
            
            return (
              <div
                key={i}
                className="absolute w-6 h-6 flex items-center justify-center text-xs font-semibold text-muted-foreground"
                style={{
                  transform: `translate(${x}px, ${y}px)`,
                }}
              >
                {i + 1}
              </div>
            );
          })}

          {/* Clock Hands */}
          <div className="absolute inset-0 flex items-center justify-center">
            {/* Hour Hand */}
            <div
              className="absolute w-1 bg-primary origin-bottom transition-smooth"
              style={{
                height: '60px',
                transform: `translateY(-30px) rotate(${(timeRemaining.hours % 12) * 30 + (timeRemaining.minutes * 0.5)}deg)`,
              }}
            />
            
            {/* Minute Hand */}
            <div
              className="absolute w-0.5 bg-accent origin-bottom transition-smooth"
              style={{
                height: '80px',
                transform: `translateY(-40px) rotate(${timeRemaining.minutes * 6 + (timeRemaining.seconds * 0.1)}deg)`,
              }}
            />
            
            {/* Second Hand */}
            <div
              className="absolute w-px bg-red-500 origin-bottom transition-all duration-1000 ease-linear"
              style={{
                height: '100px',
                transform: `translateY(-50px) rotate(${timeRemaining.seconds * 6}deg)`,
              }}
            />
            
            {/* Center Dot */}
            <div className="w-4 h-4 bg-primary rounded-full z-10 animate-pulse-glow" />
          </div>
        </div>
      </div>

      {/* Digital Display */}
      <div className="grid grid-cols-4 gap-6 text-center">
        <div className="glass-card p-4 rounded-xl transition-spring hover:scale-105">
          <div className="text-3xl font-bold gradient-text">{timeRemaining.days}</div>
          <div className="text-sm text-muted-foreground">Days</div>
        </div>
        <div className="glass-card p-4 rounded-xl transition-spring hover:scale-105">
          <div className="text-3xl font-bold gradient-text">{timeRemaining.hours}</div>
          <div className="text-sm text-muted-foreground">Hours</div>
        </div>
        <div className="glass-card p-4 rounded-xl transition-spring hover:scale-105">
          <div className="text-3xl font-bold gradient-text">{timeRemaining.minutes}</div>
          <div className="text-sm text-muted-foreground">Minutes</div>
        </div>
        <div className="glass-card p-4 rounded-xl transition-spring hover:scale-105">
          <div className="text-3xl font-bold gradient-text animate-bounce-in">{timeRemaining.seconds}</div>
          <div className="text-sm text-muted-foreground">Seconds</div>
        </div>
      </div>

      {/* Time Status */}
      {timeRemaining.total <= 0 && (
        <div className="text-center animate-bounce-in">
          <div className="text-2xl font-bold text-green-400">🎉 Event has arrived! 🎉</div>
        </div>
      )}
    </div>
  );
}