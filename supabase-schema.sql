-- Supabase SQL Schema για το Event Countdown App
-- Εκτέλεσε αυτό το script στο Supabase SQL Editor

-- Δημιουργία πίνακα events
CREATE TABLE IF NOT EXISTS public.events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    date TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Δημιουργία index για καλύτερη performance
CREATE INDEX IF NOT EXISTS events_date_idx ON public.events(date);
CREATE INDEX IF NOT EXISTS events_created_at_idx ON public.events(created_at);

-- Ενεργοποίηση Row Level Security (RLS)
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;

-- Δημιουργία policy για public access (για απλότητα - μπορείς να το αλλάξεις αργότερα)
-- Αυτό επιτρέπει σε όλους να διαβάζουν, δημιουργούν, ενημερώνουν και διαγράφουν events
CREATE POLICY "Enable all operations for all users" ON public.events
    FOR ALL USING (true) WITH CHECK (true);

-- Trigger για αυτόματη ενημέρωση του updated_at
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER events_updated_at
    BEFORE UPDATE ON public.events
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Δημιουργία μερικών sample events (προαιρετικό)
INSERT INTO public.events (name, date) VALUES 
    ('Πρωτοχρονιά 2025', '2025-01-01 00:00:00+00'),
    ('Καλοκαιρινές διακοπές', '2025-07-01 10:00:00+00')
ON CONFLICT DO NOTHING;
