# 🗄️ Supabase Setup Guide

Αυτός ο οδηγός θα σε βοηθήσει να στήσεις τη βάση δεδομένων για το Event Countdown App.

## 📋 Βήματα Setup

### 1. Δημιουργία Supabase Account

1. Πήγαινε στο [supabase.com](https://supabase.com)
2. <PERSON><PERSON><PERSON><PERSON> κλικ στο "Start your project"
3. Sign up με GitHub, Google ή email
4. Επιβεβαίωσε το email σου

### 2. Δημιουργία Project

1. Στο Supabase dashboard, κάνε κλικ "New Project"
2. Επέλεξε την οργάνωσή σου
3. Συμπλήρωσε:
   - **Name**: `event-countdown` (ή όπως θέλεις)
   - **Database Password**: Δημιούργησε έναν ισχυρό κωδικό (κράτησέ τον!)
   - **Region**: Επέλεξε την πιο κοντινή περιοχή (Europe West για Ελλάδα)
4. Κάνε κλικ "Create new project"

⏳ **Περίμενε 2-3 λεπτά** για να δημιουργηθεί το project.

### 3. Εκτέλεση SQL Schema

1. Στο Supabase dashboard, πήγαινε στο **SQL Editor** (αριστερό menu)
2. Κάνε κλικ "New Query"
3. Αντίγραψε και επικόλλησε όλο το περιεχόμενο από το αρχείο `supabase-schema.sql`
4. Κάνε κλικ "Run" (ή Ctrl+Enter)

✅ Θα πρέπει να δεις το μήνυμα "Success. No rows returned"

### 4. Λήψη API Keys

1. Πήγαινε στο **Settings** → **API** (αριστερό menu)
2. Βρες τα εξής:
   - **Project URL**: `https://xxxxx.supabase.co`
   - **anon public key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### 5. Environment Variables

1. Στο root του project, αντίγραψε το `.env.example` ως `.env`:
```bash
cp .env.example .env
```

2. Άνοιξε το `.env` file και συμπλήρωσε:
```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

### 6. Έλεγχος Σύνδεσης

1. Εκκίνησε το development server:
```bash
npm run dev
```

2. Άνοιξε το app στο browser
3. Δοκίμασε να δημιουργήσεις ένα event
4. Ελέγξε στο Supabase dashboard → **Table Editor** → **events** αν εμφανίστηκε το event

## 🔒 Security Settings

Το app χρησιμοποιεί **Row Level Security (RLS)** με public access για απλότητα. 

### Για Production (Προτεινόμενο):

1. Πήγαινε στο **Authentication** → **Settings**
2. Ενεργοποίησε τους providers που θέλεις (Email, Google, GitHub, κλπ)
3. Ενημέρωσε τα RLS policies για να επιτρέπουν access μόνο σε authenticated users

## 🚨 Troubleshooting

### Σφάλμα: "Missing Supabase environment variables"
- Ελέγξε ότι το `.env` file υπάρχει στο root του project
- Ελέγξε ότι τα variable names είναι σωστά (`VITE_` prefix)
- Κάνε restart το dev server μετά από αλλαγές στο `.env`

### Σφάλμα: "Failed to fetch"
- Ελέγξε το Project URL (πρέπει να τελειώνει σε `.supabase.co`)
- Ελέγξε ότι το project είναι active στο Supabase dashboard
- Ελέγξε τη σύνδεση internet

### Events δεν εμφανίζονται
- Ελέγξε στο Supabase dashboard → **Table Editor** → **events**
- Ελέγξε το browser console για errors
- Ελέγξε ότι το SQL schema εκτελέστηκε σωστά

## 📊 Monitoring

Στο Supabase dashboard μπορείς να παρακολουθήσεις:
- **Database**: Queries, performance, storage
- **API**: Request logs, response times
- **Auth**: User activity (αν ενεργοποιήσεις authentication)

## 💰 Pricing

- **Free Tier**: 500MB database, 2GB bandwidth/μήνα
- **Pro**: $25/μήνα για περισσότερους πόρους
- Για personal projects, το free tier είναι αρκετό!

---

🎉 **Συγχαρητήρια!** Το Supabase είναι έτοιμο και το app σου μπορεί να αποθηκεύει events στη βάση δεδομένων!
