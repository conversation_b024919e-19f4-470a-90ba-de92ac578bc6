import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Clock, Plus } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface Event {
  id: string;
  name: string;
  date: Date;
}

interface EventFormProps {
  onEventCreate: (event: Event) => void;
}

export function EventForm({ onEventCreate }: EventFormProps) {
  const [eventName, setEventName] = useState("");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [selectedTime, setSelectedTime] = useState("12:00");
  const [isOpen, setIsOpen] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!eventName.trim() || !selectedDate) {
      return;
    }

    // Combine date and time
    const [hours, minutes] = selectedTime.split(':').map(Number);
    const eventDateTime = new Date(selectedDate);
    eventDateTime.setHours(hours, minutes, 0, 0);

    const newEvent: Event = {
      id: crypto.randomUUID(),
      name: eventName.trim(),
      date: eventDateTime,
    };

    onEventCreate(newEvent);
    
    // Reset form
    setEventName("");
    setSelectedDate(undefined);
    setSelectedTime("12:00");
    setIsOpen(false);
  };

  return (
    <div className="w-full max-w-md">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full glass-card border-primary/50 hover:bg-primary/10 transition-spring group"
          >
            <Plus className="w-4 h-4 mr-2 group-hover:rotate-90 transition-spring" />
            Add New Event
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 glass-card border-primary/30" align="start">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="eventName" className="text-sm font-medium">
                Event Name
              </Label>
              <Input
                id="eventName"
                placeholder="Enter event name..."
                value={eventName}
                onChange={(e) => setEventName(e.target.value)}
                className="glass-card border-primary/30 focus:border-primary"
                required
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Event Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal glass-card border-primary/30",
                      !selectedDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 glass-card border-primary/30" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    disabled={(date) => date < new Date()}
                    initialFocus
                    className="p-3 pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="eventTime" className="text-sm font-medium">
                Event Time
              </Label>
              <div className="relative">
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="eventTime"
                  type="time"
                  value={selectedTime}
                  onChange={(e) => setSelectedTime(e.target.value)}
                  className="pl-10 glass-card border-primary/30 focus:border-primary"
                  required
                />
              </div>
            </div>

            <div className="flex gap-2 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                className="flex-1 glass-card border-primary/30"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1 bg-gradient-to-r from-primary to-accent hover:opacity-90 transition-smooth"
              >
                Create Event
              </Button>
            </div>
          </form>
        </PopoverContent>
      </Popover>
    </div>
  );
}