@tailwind base;
@tailwind components;
@tailwind utilities;

/* Countdown Timer Design System - Modern gradients and animations */

@layer base {
  :root {
    /* Core theme colors */
    --background: 240 10% 8%;
    --foreground: 240 10% 98%;

    --card: 240 10% 12%;
    --card-foreground: 240 10% 98%;

    --popover: 240 10% 12%;
    --popover-foreground: 240 10% 98%;

    /* Brand colors - Deep blue to purple gradient theme */
    --primary: 250 100% 60%;
    --primary-foreground: 240 10% 98%;
    --primary-glow: 250 100% 70%;

    --secondary: 240 20% 20%;
    --secondary-foreground: 240 10% 98%;

    --muted: 240 10% 15%;
    --muted-foreground: 240 5% 60%;

    --accent: 260 100% 65%;
    --accent-foreground: 240 10% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 240 10% 98%;

    --border: 240 10% 20%;
    --input: 240 10% 20%;
    --ring: 250 100% 60%;

    --radius: 1rem;

    /* Gradient definitions */
    --gradient-primary: linear-gradient(135deg, hsl(250 100% 60%) 0%, hsl(260 100% 65%) 100%);
    --gradient-background: linear-gradient(135deg, hsl(240 10% 8%) 0%, hsl(245 15% 12%) 50%, hsl(250 20% 16%) 100%);
    --gradient-card: linear-gradient(135deg, hsl(240 10% 12%) 0%, hsl(245 15% 16%) 100%);
    --gradient-glow: radial-gradient(circle at 50% 50%, hsl(250 100% 60% / 0.3) 0%, transparent 70%);

    /* Clock and timer specific colors */
    --clock-face: 240 10% 15%;
    --clock-border: 250 100% 60%;
    --clock-hand: 250 100% 70%;
    --clock-center: 260 100% 65%;
    --clock-numbers: 240 10% 85%;

    /* Animation variables */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);

    /* Shadows and glows */
    --shadow-glow: 0 0 40px hsl(250 100% 60% / 0.4);
    --shadow-card: 0 10px 30px hsl(240 10% 4% / 0.6);
    --shadow-elegant: 0 20px 60px hsl(240 10% 4% / 0.8);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background: var(--gradient-background);
    min-height: 100vh;
  }
}

@layer components {
  /* Clock animations */
  @keyframes clock-tick {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(6deg); }
  }

  @keyframes glow-pulse {
    0%, 100% { box-shadow: var(--shadow-glow); }
    50% { box-shadow: 0 0 60px hsl(250 100% 60% / 0.6); }
  }

  @keyframes fade-in-up {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scale-in {
    0% {
      opacity: 0;
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Utility classes */
  .gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .glass-card {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    border: 1px solid hsl(var(--border) / 0.5);
    box-shadow: var(--shadow-card);
  }

  .glow-border {
    border: 2px solid hsl(var(--primary));
    box-shadow: var(--shadow-glow);
    animation: glow-pulse 3s ease-in-out infinite;
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.4s ease-out;
  }

  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-spring {
    transition: var(--transition-spring);
  }
}