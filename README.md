# 🕐 Χρονόμετρο Αντίστροφης Μέτρησης

Ένα όμορφο και σύγχρονο web application για τη δημιουργία countdown timers για τα σημαντικά σας events!

## 🌟 Χαρακτηριστικά

- ⏰ **Δημιουργία Events**: Προσθέστε events με όνομα, ημερομηνία και ώρα
- 🎯 **Real-time Countdown**: Παρακολουθήστε την αντίστροφη μέτρηση σε πραγματικό χρόνο
- 💾 **Persistent Storage**: Τα events αποθηκεύονται στη βάση δεδομένων (Supabase)
- 📱 **Responsive Design**: Λειτουργεί τέλεια σε όλες τις συσκευές
- 🎨 **Modern UI**: Όμορφο interface με animations και glass effects
- 🌙 **Dark Theme**: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> θέμα για καλύτερη εμπειρία χρήσης

## 🚀 Live Demo

Δείτε το app σε λειτουργία: [Event Countdown Timer](https://your-vercel-url.vercel.app)

## 🛠️ Τεχνολογίες

Αυτό το project είναι χτισμένο με:

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS + shadcn/ui
- **Database**: Supabase (PostgreSQL)
- **Deployment**: Vercel
- **Icons**: Lucide React

## 📦 Εγκατάσταση

### Προαπαιτούμενα

- Node.js (v18 ή νεότερη)
- npm ή yarn
- Supabase account (δωρεάν)

### Βήματα Εγκατάστασης

1. **Clone το repository**
```bash
git clone https://github.com/Kontses/event-clock-countdown.git
cd event-clock-countdown
```

2. **Εγκατάσταση dependencies**
```bash
npm install
```

3. **Setup Supabase**
   - Δημιουργήστε έναν δωρεάν λογαριασμό στο [Supabase](https://supabase.com)
   - Δημιουργήστε ένα νέο project
   - Πηγαίνετε στο SQL Editor και εκτελέστε το script από το αρχείο `supabase-schema.sql`

4. **Environment Variables**
```bash
# Αντιγράψτε το .env.example ως .env
cp .env.example .env

# Συμπληρώστε τις τιμές από το Supabase dashboard
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

5. **Εκκίνηση development server**
```bash
npm run dev
```

Το app θα είναι διαθέσιμο στο `http://localhost:8080`

## 🗄️ Database Schema

Το app χρησιμοποιεί έναν απλό πίνακα `events`:

```sql
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    date TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🚀 Deployment

### Vercel (Προτεινόμενο)

1. Push το code στο GitHub
2. Συνδέστε το repository στο [Vercel](https://vercel.com)
3. Προσθέστε τα environment variables στο Vercel dashboard
4. Deploy!

### Άλλες επιλογές

- **Netlify**: Drag & drop το `dist` folder
- **GitHub Pages**: Για static hosting (χωρίς Supabase)

## 🔧 Scripts

```bash
# Development
npm run dev

# Build για production
npm run build

# Preview του build
npm run preview

# Linting
npm run lint
```

## 📁 Δομή Project

```
src/
├── components/          # React components
│   ├── ui/             # shadcn/ui components
│   ├── CountdownClock.tsx
│   ├── EventForm.tsx
│   └── EventList.tsx
├── lib/                # Utilities
│   ├── supabase.ts     # Supabase client & operations
│   └── utils.ts        # Helper functions
├── pages/              # Page components
│   ├── Index.tsx       # Main page
│   └── NotFound.tsx    # 404 page
└── index.css          # Global styles
```

## 🤝 Contributing

Contributions are welcome! Παρακαλώ:

1. Fork το repository
2. Δημιουργήστε ένα feature branch (`git checkout -b feature/amazing-feature`)
3. Commit τις αλλαγές σας (`git commit -m 'Add amazing feature'`)
4. Push στο branch (`git push origin feature/amazing-feature`)
5. Ανοίξτε ένα Pull Request

## 📝 License

Αυτό το project είναι open source και διαθέσιμο υπό την [MIT License](LICENSE).

## 🙏 Acknowledgments

- [shadcn/ui](https://ui.shadcn.com/) για τα όμορφα UI components
- [Supabase](https://supabase.com/) για την εύκολη backend λύση
- [Vercel](https://vercel.com/) για το δωρεάν hosting
- [Lucide](https://lucide.dev/) για τα icons

---

Φτιάχτηκε με ❤️ για να κάνει την αναμονή πιο διασκεδαστική!

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
